<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFlow AI - 创建任务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background: #F2F2F7;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #F2F2F7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .nav-bar {
            height: 44px;
            background: #F2F2F7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        .input-field {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: none;
            font-size: 18px;
            line-height: 1.4;
            min-height: 120px;
            resize: none;
            outline: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .input-field::placeholder {
            color: #C7C7CC;
            font-weight: 400;
        }
        
        .ai-suggestion {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            border-radius: 16px;
            padding: 16px;
            color: white;
            margin-bottom: 24px;
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .suggestion-item {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }
        
        .suggestion-item:active {
            transform: scale(0.98);
            background: rgba(255,255,255,0.25);
        }
        
        .option-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        .option-card:active {
            transform: scale(0.98);
        }
        
        .priority-selector {
            display: flex;
            gap: 12px;
            margin-top: 12px;
        }
        
        .priority-option {
            flex: 1;
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }
        
        .priority-high {
            background: #FFE5E5;
            color: #FF3B30;
        }
        
        .priority-medium {
            background: #FFF4E5;
            color: #FF9500;
        }
        
        .priority-low {
            background: #E5F7E5;
            color: #34C759;
        }
        
        .priority-option.selected {
            border-color: currentColor;
            transform: scale(1.05);
        }
        
        .create-button {
            background: #007AFF;
            color: white;
            border-radius: 16px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-top: 24px;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        
        .create-button:active {
            transform: scale(0.98);
        }
        
        .voice-button {
            position: fixed;
            bottom: 120px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #FF3B30;
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(255,59,48,0.3);
            transition: all 0.2s ease;
        }
        
        .voice-button:active {
            transform: scale(0.95);
        }
        
        .voice-button.recording {
            animation: pulse 1s infinite;
            background: #FF6B6B;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <button class="text-blue-500 font-medium">
            <i class="fas fa-chevron-left mr-2"></i>返回
        </button>
        <h1 class="font-semibold text-lg">新建任务</h1>
        <button class="text-blue-500 font-medium">完成</button>
    </div>

    <!-- 主内容区域 -->
    <div class="px-5 pt-6 pb-32">
        <!-- 主输入框 -->
        <textarea 
            class="input-field w-full" 
            placeholder="告诉我你想做什么..."
            id="taskInput"
        ></textarea>

        <!-- AI 建议卡片 -->
        <div class="ai-suggestion" id="aiSuggestion" style="display: none;">
            <div class="flex items-center mb-3">
                <i class="fas fa-robot mr-2"></i>
                <span class="font-semibold">AI 建议</span>
            </div>
            
            <div class="suggestion-item">
                <div class="font-medium mb-1">建议时间</div>
                <div class="text-sm opacity-90">明天上午 9:00 - 11:00</div>
                <div class="text-xs opacity-75 mt-1">基于您的日程安排</div>
            </div>
            
            <div class="suggestion-item">
                <div class="font-medium mb-1">优先级</div>
                <div class="text-sm opacity-90">高优先级</div>
                <div class="text-xs opacity-75 mt-1">与重要项目相关</div>
            </div>
            
            <div class="suggestion-item">
                <div class="font-medium mb-1">相关任务</div>
                <div class="text-sm opacity-90">准备演示文稿、收集用户反馈</div>
                <div class="text-xs opacity-75 mt-1">可能需要协调的任务</div>
            </div>
        </div>

        <!-- 时间设置 -->
        <div class="option-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-clock text-blue-500 mr-3"></i>
                    <div>
                        <div class="font-medium">时间</div>
                        <div class="text-sm text-gray-500">明天 9:00 AM</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 优先级设置 -->
        <div class="option-card">
            <div class="flex items-center mb-3">
                <i class="fas fa-flag text-orange-500 mr-3"></i>
                <span class="font-medium">优先级</span>
            </div>
            <div class="priority-selector">
                <button class="priority-option priority-high selected">
                    <i class="fas fa-exclamation-triangle mb-1"></i>
                    <div>高</div>
                </button>
                <button class="priority-option priority-medium">
                    <i class="fas fa-minus mb-1"></i>
                    <div>中</div>
                </button>
                <button class="priority-option priority-low">
                    <i class="fas fa-arrow-down mb-1"></i>
                    <div>低</div>
                </button>
            </div>
        </div>

        <!-- 分类设置 -->
        <div class="option-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-tag text-purple-500 mr-3"></i>
                    <div>
                        <div class="font-medium">分类</div>
                        <div class="text-sm text-gray-500">工作</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 提醒设置 -->
        <div class="option-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-bell text-green-500 mr-3"></i>
                    <div>
                        <div class="font-medium">提醒</div>
                        <div class="text-sm text-gray-500">提前 15 分钟</div>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建按钮 -->
        <button class="create-button w-full">
            <i class="fas fa-plus mr-2"></i>
            创建任务
        </button>
    </div>

    <!-- 语音输入按钮 -->
    <button class="voice-button" id="voiceButton">
        <i class="fas fa-microphone"></i>
    </button>

    <script>
        // 模拟 AI 建议显示
        const taskInput = document.getElementById('taskInput');
        const aiSuggestion = document.getElementById('aiSuggestion');
        const voiceButton = document.getElementById('voiceButton');
        
        let suggestionTimeout;
        
        taskInput.addEventListener('input', function() {
            clearTimeout(suggestionTimeout);
            if (this.value.length > 5) {
                suggestionTimeout = setTimeout(() => {
                    aiSuggestion.style.display = 'block';
                }, 300);
            } else {
                aiSuggestion.style.display = 'none';
            }
        });
        
        // 优先级选择
        document.querySelectorAll('.priority-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.priority-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 语音录制模拟
        voiceButton.addEventListener('click', function() {
            this.classList.toggle('recording');
            const icon = this.querySelector('i');
            
            if (this.classList.contains('recording')) {
                icon.className = 'fas fa-stop';
                taskInput.placeholder = '正在聆听...';
            } else {
                icon.className = 'fas fa-microphone';
                taskInput.placeholder = '告诉我你想做什么...';
                taskInput.value = '完成产品原型设计并准备演示';
                taskInput.dispatchEvent(new Event('input'));
            }
        });
    </script>
</body>
</html>
