<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFlow AI - 日历</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background: #F2F2F7;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #F2F2F7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .calendar-header {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #E5E5EA;
        }
        
        .month-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .month-title {
            font-size: 24px;
            font-weight: 700;
            color: #000;
        }
        
        .nav-button {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: #F2F2F7;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007AFF;
            transition: all 0.2s ease;
        }
        
        .nav-button:active {
            transform: scale(0.95);
            background: #E5E5EA;
        }
        
        .weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .weekday {
            text-align: center;
            font-size: 12px;
            font-weight: 600;
            color: #8E8E93;
            padding: 8px 0;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            position: relative;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .calendar-day:active {
            transform: scale(0.95);
        }
        
        .calendar-day.other-month {
            color: #C7C7CC;
        }
        
        .calendar-day.today {
            background: #007AFF;
            color: white;
        }
        
        .calendar-day.selected {
            background: #007AFF;
            color: white;
        }
        
        .calendar-day.has-tasks {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .task-indicator {
            position: absolute;
            bottom: 4px;
            display: flex;
            gap: 2px;
        }
        
        .task-dot {
            width: 4px;
            height: 4px;
            border-radius: 2px;
        }
        
        .dot-high { background: #FF3B30; }
        .dot-medium { background: #FF9500; }
        .dot-low { background: #34C759; }
        
        .timeline-section {
            background: white;
            margin-top: 8px;
            border-radius: 16px 16px 0 0;
            flex: 1;
            overflow: hidden;
        }
        
        .timeline-header {
            padding: 20px 20px 16px;
            border-bottom: 1px solid #F2F2F7;
        }
        
        .timeline-title {
            font-size: 20px;
            font-weight: 700;
            color: #000;
            margin-bottom: 4px;
        }
        
        .timeline-date {
            font-size: 14px;
            color: #8E8E93;
        }
        
        .timeline-content {
            padding: 0 20px 100px;
            overflow-y: auto;
            height: calc(100vh - 400px);
        }
        
        .time-slot {
            display: flex;
            margin-bottom: 16px;
            position: relative;
        }
        
        .time-label {
            width: 60px;
            font-size: 12px;
            color: #8E8E93;
            font-weight: 500;
            padding-top: 2px;
            flex-shrink: 0;
        }
        
        .time-content {
            flex: 1;
            margin-left: 16px;
            position: relative;
        }
        
        .time-line {
            position: absolute;
            left: -8px;
            top: 8px;
            bottom: -8px;
            width: 1px;
            background: #E5E5EA;
        }
        
        .task-block {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }
        
        .task-block.priority-high { border-left-color: #FF3B30; }
        .task-block.priority-medium { border-left-color: #FF9500; }
        .task-block.priority-low { border-left-color: #34C759; }
        
        .task-title {
            font-weight: 600;
            color: #000;
            margin-bottom: 4px;
        }
        
        .task-time {
            font-size: 12px;
            color: #8E8E93;
        }
        
        .task-ai-tag {
            display: inline-flex;
            align-items: center;
            background: #E3F2FD;
            color: #1976D2;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-top: 4px;
        }
        
        .current-time-indicator {
            position: absolute;
            left: -12px;
            width: calc(100% + 24px);
            height: 2px;
            background: #FF3B30;
            border-radius: 1px;
            z-index: 2;
        }
        
        .current-time-indicator::before {
            content: '';
            position: absolute;
            left: -4px;
            top: -3px;
            width: 8px;
            height: 8px;
            background: #FF3B30;
            border-radius: 50%;
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: rgba(248,248,248,0.95);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8E8E93;
            font-size: 10px;
            transition: color 0.2s ease;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 日历头部 -->
    <div class="calendar-header">
        <div class="month-selector">
            <button class="nav-button">
                <i class="fas fa-chevron-left"></i>
            </button>
            <h1 class="month-title">2023年12月</h1>
            <button class="nav-button">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <!-- 星期标题 -->
        <div class="weekdays">
            <div class="weekday">日</div>
            <div class="weekday">一</div>
            <div class="weekday">二</div>
            <div class="weekday">三</div>
            <div class="weekday">四</div>
            <div class="weekday">五</div>
            <div class="weekday">六</div>
        </div>

        <!-- 日历网格 -->
        <div class="calendar-grid">
            <!-- 上月末尾 -->
            <div class="calendar-day other-month">26</div>
            <div class="calendar-day other-month">27</div>
            <div class="calendar-day other-month">28</div>
            <div class="calendar-day other-month">29</div>
            <div class="calendar-day other-month">30</div>
            
            <!-- 本月 -->
            <div class="calendar-day">1</div>
            <div class="calendar-day">2</div>
            <div class="calendar-day">3</div>
            <div class="calendar-day">4</div>
            <div class="calendar-day">5</div>
            <div class="calendar-day">6</div>
            <div class="calendar-day">7</div>
            <div class="calendar-day">8</div>
            <div class="calendar-day">9</div>
            <div class="calendar-day">10</div>
            <div class="calendar-day">11</div>
            <div class="calendar-day">12</div>
            <div class="calendar-day">13</div>
            <div class="calendar-day">14</div>
            <div class="calendar-day">15</div>
            <div class="calendar-day today selected">
                16
                <div class="task-indicator">
                    <div class="task-dot dot-high"></div>
                    <div class="task-dot dot-medium"></div>
                    <div class="task-dot dot-low"></div>
                </div>
            </div>
            <div class="calendar-day has-tasks">
                17
                <div class="task-indicator">
                    <div class="task-dot dot-medium"></div>
                    <div class="task-dot dot-low"></div>
                </div>
            </div>
            <div class="calendar-day">18</div>
            <div class="calendar-day has-tasks">
                19
                <div class="task-indicator">
                    <div class="task-dot dot-high"></div>
                </div>
            </div>
            <div class="calendar-day">20</div>
            <div class="calendar-day">21</div>
            <div class="calendar-day">22</div>
            <div class="calendar-day">23</div>
            <div class="calendar-day">24</div>
            <div class="calendar-day">25</div>
            <div class="calendar-day">26</div>
            <div class="calendar-day">27</div>
            <div class="calendar-day">28</div>
            <div class="calendar-day">29</div>
            <div class="calendar-day">30</div>
            <div class="calendar-day">31</div>
            
            <!-- 下月开头 -->
            <div class="calendar-day other-month">1</div>
            <div class="calendar-day other-month">2</div>
            <div class="calendar-day other-month">3</div>
            <div class="calendar-day other-month">4</div>
            <div class="calendar-day other-month">5</div>
            <div class="calendar-day other-month">6</div>
        </div>
    </div>

    <!-- 时间轴部分 -->
    <div class="timeline-section">
        <div class="timeline-header">
            <h2 class="timeline-title">今日日程</h2>
            <p class="timeline-date">12月16日，星期六</p>
        </div>

        <div class="timeline-content">
            <!-- 早晨 -->
            <div class="time-slot">
                <div class="time-label">7:00</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-low">
                        <div class="task-title">晨间锻炼</div>
                        <div class="task-time">7:00 - 8:00</div>
                    </div>
                </div>
            </div>

            <!-- 上午 -->
            <div class="time-slot">
                <div class="time-label">9:00</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-high">
                        <div class="task-title">完成产品原型设计</div>
                        <div class="task-time">9:00 - 11:00</div>
                        <div class="task-ai-tag">
                            <i class="fas fa-robot mr-1"></i>AI 优化
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前时间指示器 -->
            <div class="time-slot">
                <div class="time-label">10:30</div>
                <div class="time-content">
                    <div class="current-time-indicator"></div>
                </div>
            </div>

            <div class="time-slot">
                <div class="time-label">11:30</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-medium">
                        <div class="task-title">团队会议准备</div>
                        <div class="task-time">11:30 - 12:30</div>
                    </div>
                </div>
            </div>

            <!-- 下午 -->
            <div class="time-slot">
                <div class="time-label">14:00</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-medium">
                        <div class="task-title">团队会议</div>
                        <div class="task-time">14:00 - 15:00</div>
                    </div>
                </div>
            </div>

            <div class="time-slot">
                <div class="time-label">16:00</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-low">
                        <div class="task-title">整理桌面文件</div>
                        <div class="task-time">16:00 - 17:00</div>
                    </div>
                </div>
            </div>

            <!-- 晚上 -->
            <div class="time-slot">
                <div class="time-label">19:00</div>
                <div class="time-content">
                    <div class="time-line"></div>
                    <div class="task-block priority-low">
                        <div class="task-title">阅读技术文档</div>
                        <div class="task-time">19:00 - 20:00</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>今天</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-calendar"></i>
            <span>日历</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-robot"></i>
            <span>AI助手</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 日历日期点击
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.addEventListener('click', function() {
                if (!this.classList.contains('other-month')) {
                    document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));
                    this.classList.add('selected');
                }
            });
        });

        // 月份导航
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function() {
                // 这里可以添加月份切换逻辑
                console.log('月份切换');
            });
        });
    </script>
</body>
</html>
