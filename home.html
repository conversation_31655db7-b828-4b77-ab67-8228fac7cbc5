<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFlow AI - 今日任务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background: #F2F2F7;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #F2F2F7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .ai-pulse {
            width: 8px;
            height: 8px;
            background: #34C759;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
        
        .task-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.2s ease;
        }
        
        .task-card:active {
            transform: scale(0.98);
        }
        
        .priority-high { border-left-color: #FF3B30; }
        .priority-medium { border-left-color: #FF9500; }
        .priority-low { border-left-color: #34C759; }
        
        .progress-bar {
            height: 2px;
            background: #E5E5EA;
            border-radius: 1px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: #007AFF;
            transition: width 0.3s ease;
        }
        
        .floating-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
            transition: all 0.2s ease;
        }
        
        .floating-button:active {
            transform: scale(0.95);
        }
        
        .ai-status-card {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            border-radius: 16px;
            padding: 16px;
            color: white;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }
        
        .ai-status-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 10s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: rgba(248,248,248,0.95);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8E8E93;
            font-size: 10px;
            transition: color 0.2s ease;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="px-5 pt-4 pb-24">
        <!-- 头部标题 -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-black">今天</h1>
                <p class="text-gray-500 text-sm mt-1">12月16日，星期六</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="ai-pulse"></div>
                <button class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                    <i class="fas fa-user text-gray-600 text-sm"></i>
                </button>
            </div>
        </div>

        <!-- AI 状态卡片 -->
        <div class="ai-status-card relative z-10">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-semibold text-lg">AI 正在优化您的日程</h3>
                    <p class="text-white/80 text-sm mt-1">3 个任务已重新安排</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-robot text-xl"></i>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-12 p-4 text-center">
                <div class="text-2xl font-bold text-black">8</div>
                <div class="text-xs text-gray-500 mt-1">待完成</div>
            </div>
            <div class="bg-white rounded-12 p-4 text-center">
                <div class="text-2xl font-bold text-green-500">5</div>
                <div class="text-xs text-gray-500 mt-1">已完成</div>
            </div>
            <div class="bg-white rounded-12 p-4 text-center">
                <div class="text-2xl font-bold text-orange-500">2</div>
                <div class="text-xs text-gray-500 mt-1">进行中</div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3">
            <!-- 高优先级任务 -->
            <div class="task-card priority-high">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="font-semibold text-black text-base">完成产品原型设计</h3>
                        <p class="text-gray-500 text-sm mt-1">9:00 - 11:00</p>
                        <div class="flex items-center mt-2 space-x-2">
                            <span class="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded-full">
                                <i class="fas fa-robot mr-1"></i>AI 优化
                            </span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>
                    <button class="w-6 h-6 border-2 border-gray-300 rounded-full"></button>
                </div>
            </div>

            <!-- 中优先级任务 -->
            <div class="task-card priority-medium">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="font-semibold text-black text-base">团队会议准备</h3>
                        <p class="text-gray-500 text-sm mt-1">14:00 - 15:00</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%"></div>
                        </div>
                    </div>
                    <button class="w-6 h-6 border-2 border-gray-300 rounded-full"></button>
                </div>
            </div>

            <!-- 低优先级任务 -->
            <div class="task-card priority-low">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="font-semibold text-black text-base">整理桌面文件</h3>
                        <p class="text-gray-500 text-sm mt-1">随时</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    <button class="w-6 h-6 border-2 border-gray-300 rounded-full"></button>
                </div>
            </div>

            <!-- 已完成任务 -->
            <div class="task-card priority-low opacity-60">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="font-semibold text-black text-base line-through">晨间锻炼</h3>
                        <p class="text-gray-500 text-sm mt-1">7:00 - 8:00</p>
                        <div class="progress-bar">
                            <div class="progress-fill bg-green-500" style="width: 100%"></div>
                        </div>
                    </div>
                    <button class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动添加按钮 -->
    <button class="floating-button">
        <i class="fas fa-plus"></i>
    </button>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>今天</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-calendar"></i>
            <span>日历</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-robot"></i>
            <span>AI助手</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
