<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFlow AI - AI助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-x: hidden;
            height: 100vh;
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        
        .ai-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 44px);
            position: relative;
        }
        
        .ai-header {
            text-align: center;
            padding: 40px 20px;
            color: white;
        }
        
        .ai-avatar {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.2);
            border-radius: 60px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .ai-avatar::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 60px;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            animation: pulse-glow 3s ease-in-out infinite;
        }
        
        @keyframes pulse-glow {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }
        
        .ai-avatar i {
            font-size: 48px;
            color: white;
            z-index: 1;
        }
        
        .chat-area {
            flex: 1;
            background: white;
            border-radius: 24px 24px 0 0;
            padding: 24px 20px;
            overflow-y: auto;
            position: relative;
        }
        
        .chat-message {
            margin-bottom: 16px;
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message-ai {
            display: flex;
            align-items: flex-start;
        }
        
        .message-user {
            display: flex;
            justify-content: flex-end;
        }
        
        .message-bubble {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .bubble-ai {
            background: #F2F2F7;
            color: #000;
            border-bottom-left-radius: 6px;
        }
        
        .bubble-user {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 6px;
        }
        
        .ai-mini-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .ai-mini-avatar i {
            color: white;
            font-size: 14px;
        }
        
        .quick-actions {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid #E5E5EA;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .action-card {
            background: #F8F9FA;
            border-radius: 16px;
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
            border: 1px solid #E9ECEF;
        }
        
        .action-card:active {
            transform: scale(0.98);
            background: #E9ECEF;
        }
        
        .action-card i {
            font-size: 24px;
            margin-bottom: 8px;
            color: #007AFF;
        }
        
        .action-card .title {
            font-weight: 600;
            font-size: 14px;
            color: #000;
            margin-bottom: 4px;
        }
        
        .action-card .subtitle {
            font-size: 12px;
            color: #666;
        }
        
        .input-area {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #F8F9FA;
            border-radius: 24px;
            margin-top: 16px;
        }
        
        .input-field {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
            padding: 8px 0;
        }
        
        .input-field::placeholder {
            color: #999;
        }
        
        .voice-input {
            width: 40px;
            height: 40px;
            background: #007AFF;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.2s ease;
        }
        
        .voice-input:active {
            transform: scale(0.95);
        }
        
        .voice-input.listening {
            background: #FF3B30;
            animation: pulse 1s infinite;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #F2F2F7;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            margin-bottom: 16px;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <div class="ai-container">
        <!-- AI 头部 -->
        <div class="ai-header">
            <div class="ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <h1 class="text-2xl font-bold mb-2">TaskFlow AI</h1>
            <p class="text-white/80">我是您的智能任务助手</p>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
            <!-- AI 欢迎消息 -->
            <div class="chat-message message-ai">
                <div class="ai-mini-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-bubble bubble-ai">
                    您好！我是 TaskFlow AI，可以帮您管理任务、安排日程，还能根据您的习惯提供智能建议。有什么可以帮您的吗？
                </div>
            </div>

            <!-- 用户消息示例 -->
            <div class="chat-message message-user">
                <div class="message-bubble bubble-user">
                    帮我安排明天的工作计划
                </div>
            </div>

            <!-- AI 回复 -->
            <div class="chat-message message-ai">
                <div class="ai-mini-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-bubble bubble-ai">
                    好的！我已经分析了您的日程和任务优先级。建议明天的安排：<br><br>
                    🌅 9:00-11:00 完成产品原型设计<br>
                    ☕ 11:00-11:30 休息时间<br>
                    📋 11:30-12:30 团队会议准备<br>
                    🍽️ 12:30-13:30 午餐<br>
                    👥 14:00-15:00 团队会议<br><br>
                    需要我创建这些任务吗？
                </div>
            </div>

            <!-- 打字指示器 -->
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <div class="ai-mini-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="action-grid">
                <div class="action-card">
                    <i class="fas fa-calendar-plus"></i>
                    <div class="title">安排日程</div>
                    <div class="subtitle">智能规划时间</div>
                </div>
                <div class="action-card">
                    <i class="fas fa-lightbulb"></i>
                    <div class="title">优化建议</div>
                    <div class="subtitle">提升效率</div>
                </div>
                <div class="action-card">
                    <i class="fas fa-chart-line"></i>
                    <div class="title">进度分析</div>
                    <div class="subtitle">查看统计</div>
                </div>
                <div class="action-card">
                    <i class="fas fa-magic"></i>
                    <div class="title">智能分类</div>
                    <div class="subtitle">自动整理</div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <input type="text" class="input-field" placeholder="输入您的需求..." id="messageInput">
                <button class="voice-input" id="voiceButton">
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        const messageInput = document.getElementById('messageInput');
        const voiceButton = document.getElementById('voiceButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const chatArea = document.querySelector('.chat-area');

        // 语音输入切换
        voiceButton.addEventListener('click', function() {
            this.classList.toggle('listening');
            const icon = this.querySelector('i');
            
            if (this.classList.contains('listening')) {
                icon.className = 'fas fa-stop';
                messageInput.placeholder = '正在聆听...';
            } else {
                icon.className = 'fas fa-microphone';
                messageInput.placeholder = '输入您的需求...';
            }
        });

        // 快速操作点击
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.title').textContent;
                addUserMessage(title);
                showTypingIndicator();
                
                setTimeout(() => {
                    hideTypingIndicator();
                    addAIMessage(getAIResponse(title));
                }, 2000);
            });
        });

        // 消息发送
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && this.value.trim()) {
                addUserMessage(this.value);
                showTypingIndicator();
                
                setTimeout(() => {
                    hideTypingIndicator();
                    addAIMessage('好的，我正在为您处理这个请求...');
                }, 1500);
                
                this.value = '';
            }
        });

        function addUserMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message message-user';
            messageDiv.innerHTML = `
                <div class="message-bubble bubble-user">${text}</div>
            `;
            chatArea.insertBefore(messageDiv, typingIndicator);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function addAIMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message message-ai';
            messageDiv.innerHTML = `
                <div class="ai-mini-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-bubble bubble-ai">${text}</div>
            `;
            chatArea.insertBefore(messageDiv, typingIndicator);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        function getAIResponse(action) {
            const responses = {
                '安排日程': '我已经为您优化了明天的日程安排，将重要任务安排在您效率最高的时间段。',
                '优化建议': '根据您的工作模式，建议将创意性工作安排在上午，会议安排在下午。',
                '进度分析': '本周您已完成 85% 的计划任务，效率比上周提升了 12%！',
                '智能分类': '我已经将您的任务按项目和优先级重新分类，相关任务已自动关联。'
            };
            return responses[action] || '好的，我会帮您处理这个请求。';
        }
    </script>
</body>
</html>
