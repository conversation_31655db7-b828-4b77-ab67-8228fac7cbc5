<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskFlow AI - 高保真原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .phone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #F2F2F7;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .prototype-card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .prototype-card:hover {
            transform: translateY(-5px);
        }
        
        .prototype-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .prototype-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            text-align: center;
        }
        
        iframe {
            border: none;
            border-radius: 39px;
        }
        
        .header {
            text-align: center;
            padding: 60px 20px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 20px;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="header">
        <h1>TaskFlow AI</h1>
        <p>iOS设计语言的极致演绎 - 高保真原型展示</p>
        <div class="mt-8 text-sm opacity-75">
            <i class="fas fa-mobile-alt mr-2"></i>
            模拟 iPhone 15 Pro (393×852px) | 
            <i class="fas fa-palette mr-2"></i>
            iOS 17 设计规范 | 
            <i class="fas fa-robot mr-2"></i>
            AI 驱动体验
        </div>
    </div>

    <div class="prototype-grid">
        <!-- 主页 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-home mr-2 text-blue-500"></i>
                今日任务主页
            </h3>
            <p class="prototype-description">
                核心界面，展示今日任务列表、AI状态和快速操作
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="home.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 任务创建 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-plus mr-2 text-green-500"></i>
                任务创建界面
            </h3>
            <p class="prototype-description">
                智能任务创建，AI实时建议和自动补全
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="create-task.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- AI助手 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-robot mr-2 text-purple-500"></i>
                AI助手浮层
            </h3>
            <p class="prototype-description">
                智能对话界面，语音输入和场景化建议
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="ai-assistant.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 日历视图 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-calendar mr-2 text-orange-500"></i>
                日历视图
            </h3>
            <p class="prototype-description">
                时间轴视图，任务时间分布和日程安排
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="calendar.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 个人中心 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-user mr-2 text-indigo-500"></i>
                个人中心
            </h3>
            <p class="prototype-description">
                用户资料、统计数据和个性化设置
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="profile.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 设置页面 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-cog mr-2 text-gray-500"></i>
                设置页面
            </h3>
            <p class="prototype-description">
                应用设置、AI配置和系统偏好
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="settings.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 任务详情 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-info-circle mr-2 text-cyan-500"></i>
                任务详情
            </h3>
            <p class="prototype-description">
                详细任务信息、编辑和AI优化建议
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="task-detail.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>

        <!-- 引导页面 -->
        <div class="prototype-card">
            <h3 class="prototype-title">
                <i class="fas fa-play-circle mr-2 text-pink-500"></i>
                引导页面
            </h3>
            <p class="prototype-description">
                首次使用引导，AI功能介绍和权限设置
            </p>
            <div class="phone-frame mx-auto">
                <div class="phone-screen">
                    <iframe src="onboarding.html" width="100%" height="100%"></iframe>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-gray-900 text-white py-12 text-center">
        <div class="max-w-4xl mx-auto px-6">
            <h3 class="text-2xl font-bold mb-4">TaskFlow AI 原型设计</h3>
            <p class="text-gray-300 mb-6">
                基于iOS 17设计语言，融合AI智能体验的任务管理应用原型
            </p>
            <div class="flex justify-center space-x-8 text-sm">
                <span><i class="fas fa-code mr-2"></i>HTML + Tailwind CSS</span>
                <span><i class="fas fa-mobile-alt mr-2"></i>iPhone 15 Pro 适配</span>
                <span><i class="fas fa-palette mr-2"></i>iOS 设计规范</span>
                <span><i class="fas fa-robot mr-2"></i>AI 驱动交互</span>
            </div>
        </div>
    </footer>
</body>
</html>
